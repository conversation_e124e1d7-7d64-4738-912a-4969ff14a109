<template>
  <div class="container mt-5">
    <div class="row justify-content-center">
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h3>時間限制管理</h3>
            <small class="text-muted">開發和測試用途</small>
          </div>
          <div class="card-body">
            
            <!-- 當前狀態 -->
            <div class="alert" :class="serviceAvailable ? 'alert-success' : 'alert-danger'">
              <h5 class="alert-heading">
                <i :class="serviceAvailable ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
                服務狀態: {{ serviceAvailable ? '開放中' : '已關閉' }}
              </h5>
              <p class="mb-0">{{ statusMessage }}</p>
            </div>

            <!-- 時間信息 -->
            <div class="row mb-4">
              <div class="col-md-6">
                <div class="card bg-light">
                  <div class="card-body">
                    <h6 class="card-title">當前時間</h6>
                    <p class="card-text">{{ currentTime }}</p>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="card bg-light">
                  <div class="card-body">
                    <h6 class="card-title">服務結束時間</h6>
                    <p class="card-text">{{ endTime }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 剩餘時間 -->
            <div class="mb-4">
              <h6>剩餘時間</h6>
              <div class="progress mb-2" style="height: 25px;">
                <div 
                  class="progress-bar" 
                  :class="progressBarClass"
                  :style="{ width: progressPercentage + '%' }"
                >
                  {{ formattedTimeRemaining }}
                </div>
              </div>
            </div>

            <!-- 控制按鈕 -->
            <div class="d-grid gap-2">
              <button 
                class="btn btn-primary" 
                @click="refreshStatus"
              >
                <i class="fas fa-sync-alt"></i> 刷新狀態
              </button>
              
              <button 
                class="btn btn-warning" 
                @click="toggleTimeLimit"
              >
                <i class="fas fa-toggle-on"></i> 
                {{ timeLimitEnabled ? '禁用' : '啟用' }}時間限制 (開發用)
              </button>
              
              <button 
                class="btn btn-info" 
                @click="simulateTimeChange"
              >
                <i class="fas fa-fast-forward"></i> 模擬時間變化 (測試用)
              </button>
            </div>

            <!-- 警告信息 -->
            <div class="alert alert-warning mt-4">
              <h6><i class="fas fa-exclamation-triangle"></i> 注意事項</h6>
              <ul class="mb-0">
                <li>此頁面僅供開發和測試使用</li>
                <li>生產環境中應移除此頁面的路由</li>
                <li>時間限制的禁用只在當前瀏覽器會話中有效</li>
              </ul>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { 
  isServiceAvailable, 
  getServiceEndDate, 
  getFormattedTimeRemaining,
  getTimeRemaining,
  setTimeLimitEnabled 
} from '../utils/timeLimit'

const serviceAvailable = ref(true)
const currentTime = ref('')
const endTime = ref('')
const formattedTimeRemaining = ref('')
const timeLimitEnabled = ref(true)
let updateInterval = null

const statusMessage = computed(() => {
  if (serviceAvailable.value) {
    const remaining = getTimeRemaining()
    if (remaining.days <= 7 && !remaining.isExpired) {
      return `服務即將在 ${remaining.days} 天後結束，請注意！`
    }
    return '服務正常運行中'
  } else {
    return '服務已超過開放時間，用戶將看到服務不可用頁面'
  }
})

const progressBarClass = computed(() => {
  const remaining = getTimeRemaining()
  if (remaining.isExpired) return 'bg-danger'
  if (remaining.days <= 3) return 'bg-danger'
  if (remaining.days <= 7) return 'bg-warning'
  return 'bg-success'
})

const progressPercentage = computed(() => {
  const remaining = getTimeRemaining()
  if (remaining.isExpired) return 0
  
  // 假設總服務時間為1年（365天）
  const totalDays = 365
  const remainingDays = remaining.days + (remaining.hours / 24)
  return Math.max(0, Math.min(100, (remainingDays / totalDays) * 100))
})

const updateStatus = () => {
  serviceAvailable.value = isServiceAvailable()
  currentTime.value = new Date().toLocaleString('zh-TW')
  endTime.value = getServiceEndDate().toLocaleString('zh-TW')
  formattedTimeRemaining.value = getFormattedTimeRemaining()
}

const refreshStatus = () => {
  updateStatus()
}

const toggleTimeLimit = () => {
  timeLimitEnabled.value = !timeLimitEnabled.value
  setTimeLimitEnabled(timeLimitEnabled.value)
  updateStatus()
}

const simulateTimeChange = () => {
  // 這裡可以添加模擬時間變化的邏輯
  alert('模擬功能：可以在這裡添加時間模擬邏輯')
}

onMounted(() => {
  updateStatus()
  // 每秒更新一次
  updateInterval = setInterval(updateStatus, 1000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
.progress {
  border-radius: 10px;
}

.progress-bar {
  border-radius: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: none;
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: none;
}

.btn {
  border-radius: 8px;
  font-weight: 500;
}

.alert {
  border-radius: 10px;
}
</style>
