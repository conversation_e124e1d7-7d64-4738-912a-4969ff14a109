import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import { getCurrentUser } from '../firebase/auth'
import { getCookie } from '../utils/cookies'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'login',
      component: LoginView,
    },
    {
      path: '/register',
      name: 'register',
      component: () => import('../views/RegisterView.vue'),
    },
    {
      path: '/language',
      name: 'language',
      component: () => import('../views/LanguageSelection.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/chapters',
      name: 'chapters',
      component: () => import('../views/ChapterSelection.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/exam/:chapterId',
      name: 'exam',
      component: () => import('../views/ExamView.vue'),
      props: true,
      meta: { requiresAuth: true },
    },
    {
      path: '/history',
      name: 'history',
      component: () => import('../views/HistoryView.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/history/:id',
      name: 'examDetail',
      component: () => import('../views/ExamDetailView.vue'),
      props: true,
      meta: { requiresAuth: true },
    },
    {
      path: '/questionbank/:chapterId',
      name: 'questionBank',
      component: () => import('../views/QuestionBankView.vue'),
      props: true,
      meta: { requiresAuth: true },
    },
    {
      path: '/test-email',
      name: 'testEmail',
      component: () => import('../views/TestEmailVerification.vue'),
    },
  ],
})

// 路由守衛
router.beforeEach((to, _from, next) => {
  const requiresAuth = to.matched.some((record) => record.meta.requiresAuth)
  const currentUser = getCurrentUser()

  // 檢查 Cookie 中的登入狀態
  const isLoggedInFromCookie = getCookie('userLoggedIn') === 'true'
  const isUserLoggedIn = currentUser || isLoggedInFromCookie

  if (requiresAuth && !isUserLoggedIn) {
    // 需要認證但用戶未登入，重定向到登入頁面
    next('/')
  } else if ((to.name === 'login' || to.name === 'register') && isUserLoggedIn) {
    // 已登入用戶訪問登入或註冊頁面，重定向到語言選擇頁面
    next('/language')
  } else {
    next()
  }
})

export default router
