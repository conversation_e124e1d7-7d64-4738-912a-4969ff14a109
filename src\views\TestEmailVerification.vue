<template>
  <div class="container mt-5">
    <div class="row justify-content-center">
      <div class="col-md-6">
        <div class="card">
          <div class="card-header">
            <h3>測試郵箱驗證功能</h3>
          </div>
          <div class="card-body">
            <div class="mb-3">
              <label class="form-label">測試郵箱</label>
              <input type="email" class="form-control" v-model="testEmail" placeholder="輸入測試郵箱">
            </div>

            <div class="mb-3">
              <label class="form-label">測試密碼</label>
              <input type="password" class="form-control" v-model="testPassword" placeholder="輸入測試密碼">
            </div>

            <div class="d-grid gap-2">
              <button class="btn btn-info" @click="checkFirebase">
                檢查 Firebase 配置
              </button>

              <button class="btn btn-primary" @click="testRegister" :disabled="isLoading">
                {{ isLoading ? '註冊中...' : '測試註冊' }}
              </button>

              <button class="btn btn-success" @click="testLogin" :disabled="isLoading">
                {{ isLoading ? '登入中...' : '測試登入' }}
              </button>

              <button class="btn btn-warning" @click="testResend" :disabled="isLoading">
                {{ isLoading ? '發送中...' : '重新發送驗證郵件' }}
              </button>
            </div>

            <div v-if="message" class="alert mt-3" :class="messageType">
              {{ message }}
            </div>

            <div v-if="userInfo" class="mt-3">
              <h5>用戶信息：</h5>
              <p><strong>Email:</strong> {{ userInfo.email }}</p>
              <p><strong>Email Verified:</strong> {{ userInfo.emailVerified ? '已驗證' : '未驗證' }}</p>
              <p><strong>UID:</strong> {{ userInfo.uid }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { registerUser, loginUser, resendVerificationEmail, getCurrentUser } from '../firebase/auth'
import { checkFirebaseConfig, testFirebaseConnection } from '../utils/firebaseCheck'

const testEmail = ref('<EMAIL>')
const testPassword = ref('123456')
const isLoading = ref(false)
const message = ref('')
const messageType = ref('alert-info')
const userInfo = ref(null)

const showMessage = (msg, type = 'info') => {
  message.value = msg
  messageType.value = `alert-${type}`
  setTimeout(() => {
    message.value = ''
  }, 5000)
}

const testRegister = async () => {
  if (!testEmail.value || !testPassword.value) {
    showMessage('請填寫郵箱和密碼', 'danger')
    return
  }

  isLoading.value = true
  try {
    const result = await registerUser(testEmail.value, testPassword.value)
    if (result.success) {
      showMessage(result.message, 'success')
      userInfo.value = result.user
    } else {
      showMessage(`註冊失敗: ${result.error}`, 'danger')
    }
  } catch (error) {
    showMessage(`註冊錯誤: ${error.message}`, 'danger')
  } finally {
    isLoading.value = false
  }
}

const testLogin = async () => {
  if (!testEmail.value || !testPassword.value) {
    showMessage('請填寫郵箱和密碼', 'danger')
    return
  }

  isLoading.value = true
  try {
    const result = await loginUser(testEmail.value, testPassword.value)
    if (result.success) {
      showMessage('登入成功！', 'success')
      userInfo.value = result.user
    } else {
      showMessage(`登入失敗: ${result.message || result.error}`, 'danger')
    }
  } catch (error) {
    showMessage(`登入錯誤: ${error.message}`, 'danger')
  } finally {
    isLoading.value = false
  }
}

const testResend = async () => {
  if (!testEmail.value || !testPassword.value) {
    showMessage('請填寫郵箱和密碼', 'danger')
    return
  }

  isLoading.value = true
  try {
    const result = await resendVerificationEmail(testEmail.value, testPassword.value)
    if (result.success) {
      showMessage(result.message, 'success')
    } else {
      showMessage(`重新發送失敗: ${result.message || result.error}`, 'danger')
    }
  } catch (error) {
    showMessage(`重新發送錯誤: ${error.message}`, 'danger')
  } finally {
    isLoading.value = false
  }
}

// 檢查當前用戶狀態
const checkCurrentUser = () => {
  const user = getCurrentUser()
  if (user) {
    userInfo.value = user
    showMessage('檢測到已登入用戶', 'info')
  }
}

const checkFirebase = async () => {
  console.log('=== 開始檢查 Firebase 配置 ===')

  // 檢查配置
  const config = checkFirebaseConfig()
  console.log('配置檢查結果:', config)

  // 測試連接
  const connection = await testFirebaseConnection()
  console.log('連接測試結果:', connection)

  if (config.isConfigured && connection.success) {
    showMessage(`Firebase 配置正常 - 項目ID: ${config.projectId}`, 'success')
  } else {
    showMessage('Firebase 配置有問題，請檢查控制台日誌', 'danger')
  }
}

// 組件掛載時檢查用戶狀態
checkCurrentUser()
</script>
