import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  sendEmailVerification,
} from 'firebase/auth'
import { auth } from './config'

// 登入
export const loginUser = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password)

    // 檢查郵箱是否已驗證
    if (!userCredential.user.emailVerified) {
      // 登出用戶，因為郵箱未驗證
      await signOut(auth)
      return {
        success: false,
        error: 'email-not-verified',
        message: '請先驗證您的電子郵件地址。請檢查您的郵箱並點擊驗證連結。',
      }
    }

    return { success: true, user: userCredential.user }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 註冊
export const registerUser = async (email, password) => {
  try {
    const userCredential = await createUserWithEmailAndPassword(auth, email, password)

    // 發送驗證郵件
    await sendEmailVerification(userCredential.user)

    // 登出用戶，直到郵箱驗證完成
    await signOut(auth)

    return {
      success: true,
      user: userCredential.user,
      message: '註冊成功！我們已發送驗證郵件到您的信箱，請點擊郵件中的連結完成驗證後再登入。',
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 登出
export const logoutUser = async () => {
  try {
    await signOut(auth)
    return { success: true }
  } catch (error) {
    return { success: false, error: error.message }
  }
}

// 監聽認證狀態變化
export const onAuthStateChange = (callback) => {
  return onAuthStateChanged(auth, callback)
}

// 獲取當前用戶
export const getCurrentUser = () => {
  return auth.currentUser
}

// 重新發送驗證郵件
export const resendVerificationEmail = async (email, password) => {
  try {
    // 先登入以獲取用戶對象
    const userCredential = await signInWithEmailAndPassword(auth, email, password)

    if (userCredential.user.emailVerified) {
      await signOut(auth)
      return {
        success: false,
        error: 'already-verified',
        message: '您的郵箱已經驗證過了，可以直接登入。',
      }
    }

    // 發送驗證郵件
    await sendEmailVerification(userCredential.user)

    // 登出用戶
    await signOut(auth)

    return {
      success: true,
      message: '驗證郵件已重新發送，請檢查您的郵箱。',
    }
  } catch (error) {
    return { success: false, error: error.message }
  }
}
