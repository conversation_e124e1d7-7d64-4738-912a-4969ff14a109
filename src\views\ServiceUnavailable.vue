<template>
  <div class="col-md-4 mx-auto">
    <div class="exam-card text-center">
      <div class="icon-container">
        <i class="fas fa-clock"></i>
      </div>

      <h1 class="title">目前非開放時間</h1>

      <p class="subtitle">
        感謝您的使用，本服務已於 2025年6月1日 結束開放
      </p>

      <div class="footer-text">
        <p>&copy; {{ currentYear }} 考試練習系統</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const currentYear = computed(() => new Date().getFullYear())
</script>

<style scoped>
.container {
  max-width: 600px;
  width: 100%;
  padding: 0 20px;
}

.content-wrapper {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.icon-container {
  margin-bottom: 30px;
}

.icon-container i {
  font-size: 4rem;
  color: #667eea;
  opacity: 0.8;
}

.title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 20px;
  letter-spacing: -0.5px;
}

.subtitle {
  font-size: 1.2rem;
  color: #4a5568;
  margin-bottom: 40px;
  line-height: 1.6;
  font-weight: 400;
}


.footer-text {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.footer-text p {
  color: #718096;
  font-size: 0.9rem;
  margin: 0;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .content-wrapper {
    padding: 40px 30px;
  }

  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .icon-container i {
    font-size: 3rem;
  }
}

@media (max-width: 480px) {
  .content-wrapper {
    padding: 30px 20px;
  }

  .title {
    font-size: 1.8rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .icon-container i {
    font-size: 2.5rem;
  }
}

/* 動畫效果 */
.content-wrapper {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.icon-container i {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}
</style>
