// 時間限制配置
const TIME_LIMIT = {
  // 服務結束日期：2025年6月1日 00:00:00
  endDate: new Date('2025-06-01T00:00:00'),
  
  // 是否啟用時間限制（可以用於開發時暫時關閉）
  enabled: true
}

/**
 * 檢查服務是否在開放時間內
 * @returns {boolean} true: 服務開放中, false: 服務已關閉
 */
export const isServiceAvailable = () => {
  // 如果時間限制被禁用，則始終返回 true（用於開發）
  if (!TIME_LIMIT.enabled) {
    return true
  }
  
  const now = new Date()
  const isAvailable = now < TIME_LIMIT.endDate
  
  console.log('=== 服務時間檢查 ===')
  console.log('當前時間:', now.toLocaleString('zh-TW'))
  console.log('服務結束時間:', TIME_LIMIT.endDate.toLocaleString('zh-TW'))
  console.log('服務狀態:', isAvailable ? '開放中' : '已關閉')
  
  return isAvailable
}

/**
 * 獲取服務結束時間
 * @returns {Date} 服務結束時間
 */
export const getServiceEndDate = () => {
  return TIME_LIMIT.endDate
}

/**
 * 獲取距離服務結束的剩餘時間
 * @returns {Object} 包含天數、小時、分鐘、秒數的對象
 */
export const getTimeRemaining = () => {
  const now = new Date()
  const timeDiff = TIME_LIMIT.endDate.getTime() - now.getTime()
  
  if (timeDiff <= 0) {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      isExpired: true
    }
  }
  
  const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24))
  const hours = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000)
  
  return {
    days,
    hours,
    minutes,
    seconds,
    isExpired: false
  }
}

/**
 * 格式化剩餘時間為可讀字符串
 * @returns {string} 格式化的時間字符串
 */
export const getFormattedTimeRemaining = () => {
  const timeRemaining = getTimeRemaining()
  
  if (timeRemaining.isExpired) {
    return '服務已結束'
  }
  
  const parts = []
  
  if (timeRemaining.days > 0) {
    parts.push(`${timeRemaining.days}天`)
  }
  if (timeRemaining.hours > 0) {
    parts.push(`${timeRemaining.hours}小時`)
  }
  if (timeRemaining.minutes > 0) {
    parts.push(`${timeRemaining.minutes}分鐘`)
  }
  if (timeRemaining.seconds > 0 && timeRemaining.days === 0) {
    parts.push(`${timeRemaining.seconds}秒`)
  }
  
  return parts.length > 0 ? `剩餘 ${parts.join(' ')}` : '即將結束'
}

/**
 * 設置時間限制開關（用於開發和測試）
 * @param {boolean} enabled 是否啟用時間限制
 */
export const setTimeLimitEnabled = (enabled) => {
  TIME_LIMIT.enabled = enabled
  console.log('時間限制已', enabled ? '啟用' : '禁用')
}

/**
 * 檢查是否接近服務結束時間（最後7天）
 * @returns {boolean} true: 接近結束, false: 還有時間
 */
export const isNearServiceEnd = () => {
  const timeRemaining = getTimeRemaining()
  return !timeRemaining.isExpired && timeRemaining.days <= 7
}
