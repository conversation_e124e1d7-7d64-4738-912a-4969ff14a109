import { auth } from '../firebase/config'

// 檢查 Firebase 配置
export const checkFirebaseConfig = () => {
  console.log('=== Firebase 配置檢查 ===')
  console.log('Auth 實例:', auth)
  console.log('Auth 配置:', auth.config)
  console.log('當前用戶:', auth.currentUser)
  
  // 檢查 Firebase 項目設置
  if (auth.app) {
    console.log('Firebase App 名稱:', auth.app.name)
    console.log('Firebase 項目 ID:', auth.app.options.projectId)
    console.log('Auth 域名:', auth.app.options.authDomain)
  }
  
  return {
    isConfigured: !!auth,
    projectId: auth.app?.options?.projectId,
    authDomain: auth.app?.options?.authDomain
  }
}

// 測試基本的 Firebase 連接
export const testFirebaseConnection = async () => {
  try {
    console.log('=== 測試 Firebase 連接 ===')
    
    // 測試 Auth 服務是否可用
    const user = auth.currentUser
    console.log('當前用戶狀態:', user ? '已登入' : '未登入')
    
    return { success: true, message: 'Firebase 連接正常' }
  } catch (error) {
    console.error('Firebase 連接測試失敗:', error)
    return { success: false, error: error.message }
  }
}
