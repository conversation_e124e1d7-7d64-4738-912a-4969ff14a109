<template>
  <div class="col-6 mx-auto">
    <div class="exam-card">
      <div class="text-center mb-4">
        <h1 class="display-4 gradient-text fw-bold mb-3">📚 註冊帳號</h1>
        <p class="text-muted fs-5">建立您的考試練習帳號</p>
      </div>

      <form @submit.prevent="handleRegister">
        <div class="mb-3">
          <label class="form-label fw-semibold">電子郵件</label>
          <input type="email" class="form-control form-control-lg" v-model="email" placeholder="請輸入電子郵件" required>
        </div>

        <div class="mb-3">
          <label class="form-label fw-semibold">密碼</label>
          <input type="password" class="form-control form-control-lg" v-model="password" placeholder="請輸入密碼（至少6個字元）"
            required minlength="6">
        </div>

        <div class="mb-4">
          <label class="form-label fw-semibold">確認密碼</label>
          <input type="password" class="form-control form-control-lg" v-model="confirmPassword" placeholder="請再次輸入密碼"
            required>
        </div>

        <button type="submit"
          class="btn btn-gradient btn-lg w-100 d-flex align-items-center justify-content-center gap-2"
          :disabled="!email || !password || !confirmPassword || isLoading">
          <i class="fas fa-user-plus" v-if="!isLoading"></i>
          <div class="spinner-border spinner-border-sm" role="status" v-if="isLoading">
            <span class="visually-hidden">Loading...</span>
          </div>
          {{ isLoading ? '註冊中...' : '註冊帳號' }}
        </button>
      </form>

      <div v-if="errorMessage" class="alert alert-danger mt-3 d-flex align-items-center gap-2">
        <i class="fas fa-exclamation-triangle"></i>
        {{ errorMessage }}
      </div>

      <div v-if="successMessage" class="alert alert-success mt-3 d-flex align-items-center gap-2">
        <i class="fas fa-check-circle"></i>
        {{ successMessage }}
      </div>

      <div v-if="showResendButton" class="mt-3">
        <button type="button"
          class="btn btn-outline-primary w-100 d-flex align-items-center justify-content-center gap-2"
          @click="handleResendVerification" :disabled="isResending">
          <i class="fas fa-envelope" v-if="!isResending"></i>
          <div class="spinner-border spinner-border-sm" role="status" v-if="isResending">
            <span class="visually-hidden">Loading...</span>
          </div>
          {{ isResending ? '發送中...' : '重新發送驗證郵件' }}
        </button>
      </div>

      <div class="text-center mt-4">
        <p class="text-muted">
          已有帳號？
          <router-link to="/" class="text-decoration-none fw-semibold">
            立即登入
          </router-link>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { registerUser, resendVerificationEmail } from '../firebase/auth'

const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const errorMessage = ref('')
const successMessage = ref('')
const isLoading = ref(false)
const showResendButton = ref(false)
const isResending = ref(false)

const handleRegister = async () => {
  errorMessage.value = ''
  successMessage.value = ''
  showResendButton.value = false

  // 驗證密碼
  if (password.value !== confirmPassword.value) {
    errorMessage.value = '密碼與確認密碼不符'
    return
  }

  if (password.value.length < 6) {
    errorMessage.value = '密碼至少需要6個字元'
    return
  }

  isLoading.value = true

  try {
    const result = await registerUser(email.value, password.value)

    if (result.success) {
      successMessage.value = result.message
      showResendButton.value = true
    } else {
      // 處理 Firebase 錯誤訊息
      if (result.error.includes('email-already-in-use')) {
        errorMessage.value = '此電子郵件已被註冊'
      } else if (result.error.includes('weak-password')) {
        errorMessage.value = '密碼強度不足'
      } else if (result.error.includes('invalid-email')) {
        errorMessage.value = '電子郵件格式不正確'
      } else {
        errorMessage.value = '註冊失敗：' + result.error
      }
    }
  } catch (err) {
    errorMessage.value = '註冊過程中發生錯誤'
    console.error('註冊錯誤:', err)
  } finally {
    isLoading.value = false
  }
}

const handleResendVerification = async () => {
  if (!email.value || !password.value) {
    errorMessage.value = '請確保電子郵件和密碼欄位已填寫'
    return
  }

  isResending.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const result = await resendVerificationEmail(email.value, password.value)

    if (result.success) {
      successMessage.value = result.message
    } else {
      if (result.error === 'already-verified') {
        successMessage.value = result.message
        showResendButton.value = false
      } else {
        errorMessage.value = '重新發送失敗：' + result.error
      }
    }
  } catch (err) {
    errorMessage.value = '重新發送驗證郵件時發生錯誤'
    console.error('重新發送錯誤:', err)
  } finally {
    isResending.value = false
  }
}
</script>

<style scoped>
@media (max-width: 480px) {
  .exam-card {
    padding: 30px 20px;
  }

  .display-4 {
    font-size: 2rem !important;
  }
}
</style>
