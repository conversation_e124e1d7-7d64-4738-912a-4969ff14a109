<script setup>
import { RouterView } from 'vue-router'
import { ref, onMounted } from 'vue'
import { useExamStore } from './stores/examStore'
import { isServiceAvailable } from './utils/timeLimit'
import ServiceUnavailable from './views/ServiceUnavailable.vue'

const examStore = useExamStore()
const serviceAvailable = ref(true)

// 檢查服務是否可用
const checkServiceAvailability = () => {
  serviceAvailable.value = isServiceAvailable()
}

onMounted(() => {
  checkServiceAvailability()

  // 每分鐘檢查一次服務狀態
  setInterval(checkServiceAvailability, 60000)
})
</script>

<template>
  <div id="app">
    <!-- 如果服務不可用，顯示服務不可用頁面 -->
    <ServiceUnavailable v-if="!serviceAvailable" />

    <!-- 如果服務可用，顯示正常的路由內容 -->
    <RouterView v-else :key="$route.path + examStore.currentLanguage" />
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  width: 100%;
  min-height: 100vh;
}

body {
  margin: 0;
  padding: 0;
}
</style>
